{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft.AspNetCore.Mvc": "Warning", "Microsoft.AspNetCore.Routing": "Warning", "Microsoft.AspNetCore.Hosting": "Warning"}}, "WriteTo": [{"Name": "File", "Args": {"path": "./logs/log-.txt", "rollingInterval": "Day"}}]}, "ConnectionStrings": {"DefaultConnection": "Host=localhost;Port=5432;Database=DocsAI;Username=postgres;Password=*****"}, "AllowedHosts": "*", "ChatService": {"SystemPrompt": "You are a helpful assistant. Please answer questions based only on the provided documents. If the information is not available in the documents, state that you cannot find the relevant information.", "EmptyAnswerText": "I'm sorry, I couldn't find relevant information in your internal documents. Could you please rephrase your question or provide more context? I can only answer based on the documents provided to me.", "ContextWindowSize": 10, "AIMicroserviceBaseUrl": "https://localhost:5003/", "DocumentMicroserviceBaseUrl": "https://localhost:5002", "DocSearchLimit": 5, "DocMinRelevance": 0.7}, "Jwt": {"Secret": "DocAISystemSecretKeyForJWTTokenProNhuVayDoTaoDoThangNaoLayDuocKeyCuaTaoDo", "Issuer": "DocAI"}}