name: CI/CD Pipeline

on:
  push:
    branches: ["**"] # <PERSON><PERSON><PERSON> cho tất cả các nh<PERSON>h
  pull_request:
    branches: ["main", "master"]

env:
  DOCKER_HUB_USERNAME: ${{ secrets.DOCKER_HUB_USERNAME }}
  DOCKER_HUB_ACCESS_TOKEN: ${{ secrets.DOCKER_HUB_ACCESS_TOKEN }}
  VPS_HOST: ${{ secrets.VPS_HOST }}
  VPS_USERNAME: ${{ secrets.VPS_USERNAME }}
  VPS_PASSWORD: ${{ secrets.VPS_PASSWORD }}
jobs:
  # Job này chạy cho tất cả các nhánh - chỉ build để kiểm tra
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Build AI.API (test only)
        uses: docker/build-push-action@v4
        with:
          context: .
          file: Services/AI/AI.API/Dockerfile
          push: false # Chỉ build, không push
          tags: docai-ai-api:test

      - name: Build Auth.API (test only)
        uses: docker/build-push-action@v4
        with:
          context: .
          file: Services/Auth/Auth.API/Dockerfile
          push: false # Chỉ build, không push
          tags: docai-auth-api:test

      - name: Build Document.API (test only)
        uses: docker/build-push-action@v4
        with:
          context: .
          file: Services/Document/Document.API/Dockerfile
          push: false # Chỉ build, không push
          tags: docai-document-api:test

      - name: Build Notification.API (test only)
        uses: docker/build-push-action@v4
        with:
          context: .
          file: Services/Notification/Notification.API/Dockerfile
          push: false # Chỉ build, không push
          tags: docai-notification-api:test

      - name: Build ChatBox.API (test only)
        uses: docker/build-push-action@v4
        with:
          context: .
          file: Services/ChatBox/ChatBox.API/Dockerfile
          push: false # Chỉ build, không push
          tags: docai-chatbox-api:test

      - name: Build API Gateway (test only)
        uses: docker/build-push-action@v4
        with:
          context: .
          file: ApiGateway/Dockerfile
          push: false # Chỉ build, không push
          tags: docai-gateway:test

  # Job này chỉ chạy khi push vào main - build và push image + deploy
  build-and-push:
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Login to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ env.DOCKER_HUB_USERNAME }}
          password: ${{ env.DOCKER_HUB_ACCESS_TOKEN }}
      # Build and push AI.API
      - name: Build and push AI.API
        uses: docker/build-push-action@v4
        with:
          context: .
          file: Services/AI/AI.API/Dockerfile
          push: true
          tags: magicflexing/docai-ai-api:latest

      # Build and push Auth.API
      - name: Build and push Auth.API
        uses: docker/build-push-action@v4
        with:
          context: .
          file: Services/Auth/Auth.API/Dockerfile
          push: true
          tags: magicflexing/docai-auth-api:latest

      # Build and push Document.API
      - name: Build and push Document.API
        uses: docker/build-push-action@v4
        with:
          context: .
          file: Services/Document/Document.API/Dockerfile
          push: true
          tags: magicflexing/docai-document-api:latest

      # Build and push Notification.API
      - name: Build and push Notification.API
        uses: docker/build-push-action@v4
        with:
          context: .
          file: Services/Notification/Notification.API/Dockerfile
          push: true
          tags: magicflexing/docai-notification-api:latest

      - name: Build and push ChatBox.API
        uses: docker/build-push-action@v4
        with:
          context: .
          file: Services/ChatBox/ChatBox.API/Dockerfile
          push: true
          tags: magicflexing/docai-chatbox-api:latest

      - name: Build and push API Gateway
        uses: docker/build-push-action@v4
        with:
          context: .
          file: ApiGateway/Dockerfile
          push: true
          tags: magicflexing/docai-gateway:latest

  # Job này chỉ chạy khi build-and-push thành công (tức là khi push vào main)
  deploy:
    needs: build-and-push
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    steps:
      - name: Deploy to VPS
        uses: appleboy/ssh-action@master
        with:
          host: ${{ env.VPS_HOST }}
          username: ${{ env.VPS_USERNAME }}
          password: ${{ env.VPS_PASSWORD }}
          script: |
            export LANG=C.UTF-8
            export LC_ALL=C.UTF-8
            cd ~/DocAI || { echo "Failed to change directory" > deploy.log; exit 1; }
            echo "${{ env.DOCKER_HUB_ACCESS_TOKEN }}" | docker login -u "${{ env.DOCKER_HUB_USERNAME }}" --password-stdin >> deploy.log 2>&1 || { echo "Docker login failed" >> deploy.log; exit 1; }
            docker-compose down --rmi all --volumes --remove-orphans >> deploy.log 2>&1 || { echo "Docker-compose down failed" >> deploy.log; exit 1; }
            docker system prune -a -f >> deploy.log 2>&1 || { echo "Docker prune failed" >> deploy.log; exit 1; }
            docker-compose pull >> deploy.log 2>&1 || { echo "Docker-compose pull failed" >> deploy.log; exit 1; }
            docker-compose up -d >> deploy.log 2>&1 || { echo "Docker-compose up failed" >> deploy.log; exit 1; }
            cat deploy.log
