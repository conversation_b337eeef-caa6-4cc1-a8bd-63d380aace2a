<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="AutoMapper" Version="14.0.0" />
        <PackageReference Include="DotNetEnv" Version="3.1.1" />
        <PackageReference Include="Google.Apis.Auth" Version="1.70.0" />
        <PackageReference Include="MassTransit" Version="8.4.1" />
        <PackageReference Include="MassTransit.RabbitMQ" Version="8.4.1" />
        <PackageReference Include="Microsoft.AspNetCore.Authentication.Google" Version="9.0.7" />
        <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.4" />
        <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.4" />
        <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.4" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.4">
          <PrivateAssets>all</PrivateAssets>
          <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.4" />
        <PackageReference Include="NSwag.AspNetCore" Version="14.4.0" />
        <PackageReference Include="RabbitMQ.Client" Version="7.1.2" />
        <PackageReference Include="Scalar.AspNetCore" Version="2.4.2" />
        <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
        <PackageReference Include="Serilog.Expressions" Version="5.1.0-dev-02301" />
        <PackageReference Include="StackExchange.Redis" Version="2.8.31" />
        <PackageReference Include="Swashbuckle.AspNetCore" Version="8.1.1" />
        <PackageReference Include="Swashbuckle.AspNetCore.ReDoc" Version="8.1.1" />
        <PackageReference Include="Swashbuckle.AspNetCore.SwaggerUI" Version="8.1.1" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\..\Shared\Shared.csproj" />
      <ProjectReference Include="..\Auth.Infrastructure\Auth.Infrastructure.csproj" />
    </ItemGroup>

    <ItemGroup>
      <Content Include="..\..\..\.dockerignore">
        <Link>.dockerignore</Link>
      </Content>
    </ItemGroup>

</Project>
