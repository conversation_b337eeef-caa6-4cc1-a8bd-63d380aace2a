{
    "Logging": {
        "LogLevel": {
            "Default": "Information",
            "Microsoft.AspNetCore": "Warning"
        }
    },
    "Serilog": {
        "MinimumLevel": {
            "Default": "Information",
            "Override": {
                "Microsoft.AspNetCore.Mvc": "Warning",
                "Microsoft.AspNetCore.Routing": "Warning",
                "Microsoft.AspNetCore.Hosting": "Warning"
            }
        },
        "WriteTo": [
            {
                "Name": "File",
                "Args": {
                    "path": "./logs/log-.txt",
                    "rollingInterval": "Day"
                }
            }
        ]
    },
    "ConnectionStrings": {
        "DefaultConnection": "Host=localhost;Port=5432;Database=DocsAI;Username=*****;Password=*****"
    },
    "AllowedHosts": "*",

    "Ollama": {
        "Host": "http://127.0.0.1:11434",
        "TextGenerationModel": "deepseek-r1:1.5b", // REVIEW POINT: Model cho Text Generation (sẽ được K<PERSON> gọi)
        "EmbeddingModel": "nomic-embed-text :v1.5", // REVIEW POINT: Model cho Embedding Generation (sẽ được KM gọi)
        "Temperature": 0.7,
        "TopP": 0.9,
        "NumPredict": 1024, // Đây là NumPredict cho TextGenerationModel
        "TextMaxTokenTotal": 4096, // Ví dụ cho DeepSeek-R1:1.5b
        "EmbeddingMaxTokenTotal": 768 // Ví dụ cho nomic-embed-text (thường là 768 hoặc 1536)
    }

}
