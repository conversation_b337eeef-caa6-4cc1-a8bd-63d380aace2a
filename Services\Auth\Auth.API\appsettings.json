{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "ConnectionStrings": {"DefaultConnection": "Host=**************;Port=5432;Database=DocAIAuthData;Username=postgres;Password=**************", "Redis": "redis:6379,abortConnect=false"}, "RabbitMQ": {"Host": "rabbitmq", "Username": "guest", "Password": "guest"}, "Jwt": {"Secret": "DocAISystemSecretKeyForJWTTokenProNhuVayDoTaoDoThangNaoLayDuocKeyCuaTaoDo", "Issuer": "DocAI"}, "Email": {"SmtpServer": "smtp.gmail.com", "Port": "587", "Username": "<EMAIL>", "Password": "anuk yrxy ksyj ecga", "DisplayName": "DocAI System"}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft.AspNetCore.Mvc": "Warning", "Microsoft.AspNetCore.Routing": "Warning", "Microsoft.AspNetCore.Hosting": "Warning"}}, "WriteTo": [{"Name": "File", "Args": {"path": "./logs/log-.txt", "rollingInterval": "Day"}}]}, "AllowedHosts": "*", "GoogleOAuth": {"ClientId": "************-qhfdt8df4hvv71nt70c31os6jbkf5648.apps.googleusercontent.com", "ClientSecret": "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", "RedirectUri": "https://localhost:5001/api/auth/google/callback"}}