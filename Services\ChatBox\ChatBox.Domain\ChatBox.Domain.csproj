﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="AutoMapper" Version="14.0.0" />
      <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.6" />
      <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.6" />
      <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.4" />
      <PackageReference Include="NSwag.AspNetCore" Version="14.4.0" />
      <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
      <PackageReference Include="Swashbuckle.AspNetCore.SwaggerUI" Version="9.0.1" />
    </ItemGroup>

    <ItemGroup>
      <Folder Include="Migrations\" />
      <Folder Include="Enum\" />
      <Folder Include="Configuration\" />
    </ItemGroup>

</Project>
