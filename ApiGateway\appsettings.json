{"JWT": {"Secret": "DocAISystemSecretKeyForJWTTokenProNhuVayDoTaoDoThangNaoLayDuocKeyCuaTaoDo", "Issuer": "DocAI"}, "ServiceEndpoints": {"Auth": "https://auth.docai.asia", "Document": "https://doc.docai.asia", "AI": "https://ai.docai.asia", "Notification": "https://noti.docai.asia", "ChatBox": "https://chatbox.docai.asia"}, "ReverseProxy": {"Routes": {"auth-route": {"ClusterId": "auth-cluster", "Match": {"Path": "/api/auth/{**catch-all}"}, "Transforms": [{"PathPattern": "/api/auth/{**catch-all}"}]}, "document-route": {"ClusterId": "document-cluster", "Match": {"Path": "/api/document/{**catch-all}"}, "Transforms": [{"PathPattern": "/api/document/{**catch-all}"}]}, "ai-route": {"ClusterId": "ai-cluster", "Match": {"Path": "/api/ai/{**catch-all}"}, "Transforms": [{"PathPattern": "/api/ai/{**catch-all}"}]}, "notification-route": {"ClusterId": "notification-cluster", "Match": {"Path": "/api/notification/{**catch-all}"}, "Transforms": [{"PathPattern": "/api/notification/{**catch-all}"}]}, "chatbox-route": {"ClusterId": "chatbox-cluster", "Match": {"Path": "/api/chatbox/{**catch-all}"}, "Transforms": [{"PathPattern": "/api/chatbox/{**catch-all}"}]}}, "Clusters": {"auth-cluster": {"Destinations": {"auth1": {"Address": "https://auth.docai.asia"}, "auth2": {"Address": "https://auth1.docai.asia"}}, "LoadBalancingPolicy": "RoundR<PERSON>in", "SessionAffinity": {"Enabled": true, "Policy": "<PERSON><PERSON>", "AffinityKeyName": "YARP.Affinity", "Cookie": {"Domain": null, "HttpOnly": true, "IsEssential": true, "MaxAge": "1.00:00:00", "Path": "/", "SameSite": "Unspecified", "SecurePolicy": "None"}}}, "document-cluster": {"Destinations": {"document1": {"Address": "https://doc.docai.asia"}, "document2": {"Address": "https://doc1.docai.asia"}}, "LoadBalancingPolicy": "RoundR<PERSON>in", "SessionAffinity": {"Enabled": true, "Policy": "<PERSON><PERSON>", "AffinityKeyName": "YARP.Affinity", "Cookie": {"Domain": null, "HttpOnly": true, "IsEssential": true, "MaxAge": "1.00:00:00", "Path": "/", "SameSite": "Unspecified", "SecurePolicy": "None"}}}, "ai-cluster": {"Destinations": {"ai1": {"Address": "https://ai.docai.asia"}, "ai2": {"Address": "https://ai1.docai.asia"}}, "LoadBalancingPolicy": "RoundR<PERSON>in", "SessionAffinity": {"Enabled": true, "Policy": "<PERSON><PERSON>", "AffinityKeyName": "YARP.Affinity", "Cookie": {"Domain": null, "HttpOnly": true, "IsEssential": true, "MaxAge": "1.00:00:00", "Path": "/", "SameSite": "Unspecified", "SecurePolicy": "None"}}}, "notification-cluster": {"Destinations": {"notification1": {"Address": "https://noti.docai.asia"}, "notification2": {"Address": "https://noti1.docai.asia"}}, "LoadBalancingPolicy": "RoundR<PERSON>in", "SessionAffinity": {"Enabled": true, "Policy": "<PERSON><PERSON>", "AffinityKeyName": "YARP.Affinity", "Cookie": {"Domain": null, "HttpOnly": true, "IsEssential": true, "MaxAge": "1.00:00:00", "Path": "/", "SameSite": "Unspecified", "SecurePolicy": "None"}}}, "chatbox-cluster": {"Destinations": {"chatbox1": {"Address": "https://chatbox.docai.asia"}, "chatbox2": {"Address": "https://chatbox1.docai.asia"}}, "LoadBalancingPolicy": "RoundR<PERSON>in", "SessionAffinity": {"Enabled": true, "Policy": "<PERSON><PERSON>", "AffinityKeyName": "YARP.Affinity", "Cookie": {"Domain": null, "HttpOnly": true, "IsEssential": true, "MaxAge": "1.00:00:00", "Path": "/", "SameSite": "Unspecified", "SecurePolicy": "None"}}}}}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.AspNetCore.HttpLogging.HttpLoggingMiddleware": "Information", "Yarp": "Debug"}}, "AllowedHosts": "*"}