using AutoMapper;
using Document.API.Constants;
using Document.API.Payload.Response;
using Document.API.Services.Interfaces;
using Document.Domain.Enums;
using Document.Domain.Model;
using Document.Domain.Models;
using Document.Infrastructure.Paginate;
using Document.Infrastructure.Repository.Interfaces;
using Microsoft.EntityFrameworkCore;
using Shared.Exceptions;

namespace Document.API.Services.Implements
{
    public class BookmarkService : IBookmarkService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly ILogger<BookmarkService> _logger;

        public BookmarkService(IUnitOfWork unitOfWork, IMapper mapper, ILogger<BookmarkService> logger)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            _logger = logger;
        }

        public async Task AddBookmarkAsync(string documentId, string userId)
        {
            // 1. Check if the document version exists and is official
            var document = await _unitOfWork.GetRepository<DocumentFile>()
                .SingleOrDefaultAsync(
                    predicate: d => d.Id == documentId
                ) ?? throw new ErrorException(StatusCodes.Status404NotFound, ErrorCode.NOT_FOUND, MessageConstant.DocumentNotFound);

            // 2. Check if the bookmark already exists for this user and document version
            var existingBookmark = await _unitOfWork.GetRepository<Bookmark>()
                .SingleOrDefaultAsync(
                    predicate: b => b.UserId == userId && b.DocumentId == documentId
                );

            if (existingBookmark != null)
            {
                throw new ErrorException(StatusCodes.Status409Conflict, ErrorCode.CONFLICT, MessageConstant.DocumentAlreadyBookmarked);
            }

            // 3. Create and save the new bookmark
            var bookmark = new Bookmark
            {
                UserId = userId,
                DocumentId = documentId,
                CreatedBy = userId
            };

            await _unitOfWork.GetRepository<Bookmark>().InsertAsync(bookmark);
            await _unitOfWork.CommitAsync();

            _logger.LogInformation("User {UserId} bookmarked document version {documentId}", userId, documentId);
        }

        public async Task RemoveBookmarkAsync(string documentId, string userId)
        {
            // 1. Find the bookmark to remove
            var bookmarkToRemove = await _unitOfWork.GetRepository<Bookmark>()
                .SingleOrDefaultAsync(
                    predicate: b => b.UserId == userId && b.DocumentId == documentId
                ) ?? throw new ErrorException(StatusCodes.Status404NotFound, ErrorCode.NOT_FOUND, MessageConstant.BookmarkNotFound);

            // 2. Delete the bookmark
            _unitOfWork.GetRepository<Bookmark>().DeleteAsync(bookmarkToRemove);
            await _unitOfWork.CommitAsync();

            _logger.LogInformation("User {UserId} removed bookmark for document version {documentId}", userId, documentId);
        }

        public async Task<IPaginate<BookmarkResponse>> GetBookmarksAsync(string userId, int pageNumber, int pageSize)
        {
            var bookmarks = await _unitOfWork.GetRepository<Bookmark>().GetPagingListAsync(
                selector: b => new BookmarkResponse
                {
                    Id = b.Id,
                    DocumentId = b.Document.Id,
                    Title = b.Document.Title,
                    Description = b.Document.Description,
                    OwnerId = b.Document.OwnerId,
                    CreatedTime = b.CreatedTime
                },
                filter: null,
                predicate: b => b.UserId == userId,
                orderBy: q => q.OrderByDescending(b => b.CreatedTime),
                include: i => i.Include(b => b.Document),
                page: pageNumber,
                size: pageSize
            );

            return bookmarks;
        }
    }
}