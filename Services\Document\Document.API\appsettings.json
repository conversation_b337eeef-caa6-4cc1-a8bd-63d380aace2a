{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "JWT": {
    "Secret": "DocAISystemSecretKeyForJWTTokenProNhuVayDoTaoDoThangNaoLayDuocKeyCuaTaoDo",
    "Issuer": "DocAI"
  },
  "RabbitMQ": {
    "Host": "rabbitmq",
    "Username": "guest",
    "Password": "guest"
  },
  "DocumentEnrichment": {
    "Enabled": true,
    "TimeoutSeconds": 2
  },
  "ConnectionStrings": {
    //"DefaultConnection": "Host=localhost;Port=5432;Database=DocAIDocumentData;Username=postgres;Password=*****",
    "DefaultConnection": "Host=**************;Port=5432;Database=DocAIDocument;Username=postgres;Password=**************",
    "Redis": "redis:6379,abortConnect=false"
  },
  "Serilog": {
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Microsoft.AspNetCore.Mvc": "Warning",
        "Microsoft.AspNetCore.Routing": "Warning",
        "Microsoft.AspNetCore.Hosting": "Warning",
        "Microsoft.KernelMemory": "Error",
        "Microsoft.KernelMemory.SemanticKernel": "Error"
      }
    },
    "WriteTo": [
      {
        "Name": "File",
        "Args": {
          "path": "./logs/log-.txt",
          "rollingInterval": "Day"
        }
      }
    ]
  },
  "AllowedHosts": "*",
  "Ollama": {
    "Endpoint": "http://ollama:11434",
    //"Endpoint": "http://127.0.0.1:11434",
    "TextModel": "gemma3:1b",
    "EmbeddingModel": "nomic-embed-text:v1.5",
    "Temperature": 0.7,
    "TopP": 0.9,
    "NumPredict": 1024
  },
  "OpenRouter": {
    "Endpoint": "https://openrouter.ai/api/v1",
    "Model": "mistralai/mistral-small-3.2-24b-instruct:free",
    "APIKey": "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"
  },
  "OpenAI": {
    "APIKey": "********************************************************************************************************************************************************************",
    "EmbeddingModel": "text-embedding-3-small"
  },
  "AzureStorage": {
    "BlobStorage": {
      "ConnectionString": "DefaultEndpointsProtocol=https;AccountName=docsaivnstorage;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net",
      "ContainerName": "docsaivnstorage"
    }
  },
  "GoogleDrive": {
    "CompanyAccountEmail": "<EMAIL>",
    "ClientId": "************-qhfdt8df4hvv71nt70c31os6jbkf5648.apps.googleusercontent.com",
    "ClientSecret": "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",
    "CompanyRootFolderId": "",
    "ApplicationName": "DocAI Document Management",
    "Scopes": [
      "https://www.googleapis.com/auth/drive",
      "https://www.googleapis.com/auth/drive.file"
    ],
    "TimeoutSeconds": 300,
    "MaxRetryAttempts": 3,
    "BaseDelayMs": 1000,
    "EnableDetailedLogging": true,
    "UseCompanyAccountForWrites": true,
    "AutoShareWithDepartmentUsers": true,
    "FolderMapping": {
      "Drafts": "drafts",
      "Pending": "pending",
      "Approved": "approved",
      "Archived": "archived",
      "Public": "public"
    }
  },
  "Storage": {
    "UseGoogleDrive": true,
    "EnableFallback": true,
    "EnableMigrationMode": false
  }
}
