﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<Nullable>enable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
		<DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.4" />
		<PackageReference Include="Microsoft.Extensions.Http.Polly" Version="9.0.6" />
		<PackageReference Include="Polly" Version="8.6.1" />
		<PackageReference Include="Serilog.Expressions" Version="5.1.0-dev-02301" />
		<PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.6" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.6">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.6">
		  <PrivateAssets>all</PrivateAssets>
		  <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
	</ItemGroup>

	<ItemGroup>
		<Content Include="..\..\..\.dockerignore">
			<Link>.dockerignore</Link>
		</Content>
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\ChatBox.Infrastructure\ChatBox.Infrastructure.csproj" />
	</ItemGroup>

</Project>
