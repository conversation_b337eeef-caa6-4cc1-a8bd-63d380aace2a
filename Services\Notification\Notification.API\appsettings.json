{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft.AspNetCore.Mvc": "Warning", "Microsoft.AspNetCore.Routing": "Warning", "Microsoft.AspNetCore.Hosting": "Warning"}}, "WriteTo": [{"Name": "File", "Args": {"path": "./logs/log-.txt", "rollingInterval": "Day"}}]}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Host=localhost;Port=5432;Database=NotiDocAi;Username=postgres;Password=*****"}, "Smtp": {"Host": "smtp.gmail.com", "Port": "587", "Username": "<EMAIL>", "Password": "your_app_password", "FromAddress": "<EMAIL>", "EnableSsl": "true"}, "Quartz": {"ScanCronExpression": "0 0 7 * * ?", "CleanupCronExpression": "0 0 0 ? * SUN"}, "MicroserviceUrls": {"Auth": "http://localhost:5001"}, "JWT": {"Secret": "ThisIsAStrongAndLongSecretKeyForDocAISystemThatNobodyCanGuess123!", "Issuer": "DocAI"}}