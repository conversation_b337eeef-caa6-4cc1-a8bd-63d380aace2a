<Project Sdk="Microsoft.NET.Sdk">

  <Sdk Name="Aspire.AppHost.Sdk" Version="9.0.0" />

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsAspireHost>true</IsAspireHost>
    <UserSecretsId>e24df526-7368-40ae-8358-4bf0f1a5772c</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Aspire.Hosting.AppHost" Version="9.3.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ApiGateway\ApiGateway.csproj" />
    <ProjectReference Include="..\Services\AI\AI.API\AI.API.csproj" />
    <ProjectReference Include="..\Services\Auth\Auth.API\Auth.API.csproj" />
    <ProjectReference Include="..\Services\ChatBox\ChatBox.API\ChatBox.API.csproj" />
    <ProjectReference Include="..\Services\Document\Document.API\Document.API.csproj" />
    <ProjectReference Include="..\Services\Notification\Notification.API\Notification.API.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="..\.dockerignore">
      <Link>.dockerignore</Link>
    </Content>
  </ItemGroup>
  

</Project>
